{"universidades": [{"id": "1", "nome": "Universidade Federal do Rio de Janeiro", "sigla": "UFRJ", "tipo": "Federal", "regiao": "Sudeste", "estado": "Rio de Janeiro", "cidade": "Rio de Janeiro", "fundacao": 1920, "campus": 3, "alunos_total": 67000, "professores_total": 4200, "cursos_graduacao": 180, "cursos_pos_graduacao": 290}, {"id": "2", "nome": "Universidade de São Paulo", "sigla": "USP", "tipo": "Estadual", "regiao": "Sudeste", "estado": "São Paulo", "cidade": "São Paulo", "fundacao": 1934, "campus": 7, "alunos_total": 95000, "professores_total": 6100, "cursos_graduacao": 249, "cursos_pos_graduacao": 239}, {"id": "3", "nome": "Universidade Federal de Minas Gerais", "sigla": "UFMG", "tipo": "Federal", "regiao": "Sudeste", "estado": "Minas Gerais", "cidade": "Belo Horizonte", "fundacao": 1927, "campus": 2, "alunos_total": 49000, "professores_total": 2800, "cursos_graduacao": 75, "cursos_pos_graduacao": 90}, {"id": "4", "nome": "Universidade Federal do Rio Grande do Sul", "sigla": "UFRGS", "tipo": "Federal", "regiao": "Sul", "estado": "Rio Grande do Sul", "cidade": "Porto Alegre", "fundacao": 1934, "campus": 4, "alunos_total": 31000, "professores_total": 2600, "cursos_graduacao": 90, "cursos_pos_graduacao": 83}, {"id": "5", "nome": "Universidade de Brasília", "sigla": "UnB", "tipo": "Federal", "regiao": "Centro-Oeste", "estado": "Distrito Federal", "cidade": "Brasília", "fundacao": 1962, "campus": 4, "alunos_total": 37000, "professores_total": 2400, "cursos_graduacao": 109, "cursos_pos_graduacao": 147}], "pessoas": [{"id": "1", "nome": "Dr. <PERSON>", "tipo": "professor", "universidade_id": 1, "departamento": "Ciência da Computação", "titulacao": "Doutor", "area_pesquisa": "Inteligência Artificial", "email": "<EMAIL>", "anos_experiencia": 15}, {"id": "2", "nome": "<PERSON><PERSON><PERSON>", "tipo": "professor", "universidade_id": 2, "departamento": "Engenharia de Software", "titulacao": "<PERSON><PERSON><PERSON>", "area_pesquisa": "Engenharia de Software", "email": "<EMAIL>", "anos_experiencia": 12}, {"id": "3", "nome": "<PERSON>", "tipo": "<PERSON><PERSON>", "universidade_id": 1, "curso": "Ciência da Computação", "semestre": 6, "ano_ingresso": 2022, "email": "<EMAIL>", "status": "ativo"}, {"id": "4", "nome": "<PERSON>", "tipo": "<PERSON><PERSON>", "universidade_id": 3, "curso": "Sistemas de Informação", "semestre": 4, "ano_ingresso": 2023, "email": "<EMAIL>", "status": "ativo"}, {"id": "5", "nome": "<PERSON>", "tipo": "funcionario", "universidade_id": 2, "cargo": "Administrador de TI", "departamento": "Tecnologia da Informação", "email": "<EMAIL>", "anos_servico": 8}], "cursos": [{"id": "1", "nome": "Ciência da Computação", "tipo": "Bacharelado", "universidade_id": 1, "departamento": "Instituto de Matemática", "duracao_semestres": 8, "vagas_anuais": 80, "nota_corte": 750, "modalidade": "Presencial"}, {"id": "2", "nome": "Engenharia de Software", "tipo": "Bacharelado", "universidade_id": 2, "departamento": "Escola Politécnica", "duracao_semestres": 10, "vagas_anuais": 60, "nota_corte": 780, "modalidade": "Presencial"}, {"id": "3", "nome": "Sistemas de Informação", "tipo": "Bacharelado", "universidade_id": 3, "departamento": "Instituto de Ciências Exatas", "duracao_semestres": 8, "vagas_anuais": 50, "nota_corte": 720, "modalidade": "Presencial"}], "regioes": [{"id": "1", "nome": "Sudeste", "estados": ["São Paulo", "Rio de Janeiro", "Minas Gerais", "Espírito Santo"], "populacao": 88371433, "universidades_federais": 15, "universidades_estaduais": 12, "universidades_privadas": 890}, {"id": "2", "nome": "Sul", "estados": ["Rio Grande do Sul", "Santa Catarina", "Paraná"], "populacao": 29975984, "universidades_federais": 8, "universidades_estaduais": 7, "universidades_privadas": 320}, {"id": "3", "nome": "Centro-Oeste", "estados": ["Distrito Federal", "Goiás", "<PERSON><PERSON>", "Mato Grosso do Sul"], "populacao": 16504303, "universidades_federais": 4, "universidades_estaduais": 3, "universidades_privadas": 180}], "usuarios": [{"id": "1", "nome": "Admin User", "email": "<EMAIL>", "senha": "$2b$10$5G/Br6iY3dmHt5US6WiTCOHWHY5pRIlux12Mi./PyYgAESg1n9hY.", "role": "admin", "avatar": null, "created_at": "2024-01-01T00:00:00.000Z", "updated_at": "2025-07-19T02:01:31.979Z", "last_login": "2025-07-19T02:01:31.979Z", "is_active": true, "preferences": {"theme": "dark", "language": "pt-BR", "default_model": "llama3-70b-8192"}}, {"id": "2", "nome": "<PERSON>", "email": "<EMAIL>", "senha": "$2b$10$MIOc5OR.3rLIyUEuAWDEUunnDwYixCF1QNAd5jesjwLEhCRSiw2Pq", "role": "user", "avatar": null, "created_at": "2024-01-02T00:00:00.000Z", "updated_at": "2024-01-02T00:00:00.000Z", "last_login": "2024-01-15T09:15:00.000Z", "is_active": true, "preferences": {"theme": "light", "language": "pt-BR", "default_model": "llama3-70b-8192"}}, {"id": "82ec", "nome": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "senha": "$2b$10$g1j57SzyMUiE8HRnqEJDKeLvoR/u2hNOaEzMuM81ZGgyFDNCEL1QW", "role": "user", "created_at": "2025-08-04T20:34:55.970Z", "updated_at": "2025-08-05T17:29:15.694Z", "is_active": true, "preferences": {"theme": "dark", "language": "pt-BR", "default_model": "llama3-70b-8192"}, "last_login": "2025-08-05T17:29:15.694Z"}], "sessoes": [], "evaluations": [], "historico": [], "favoritos": [], "tokens_jwt": [{"id": "0546", "usuario_id": "82ec", "token_hash": "$2b$10$sPYB6CioUXI2NY2hVfsuHOtsaT2v7B9v1IZpQluKT6XUTFDqeQUnO", "expires_at": "2025-08-12T17:29:15.791Z", "created_at": "2025-08-05T17:29:15.791Z", "is_revoked": true, "device_info": "Unknown Device"}]}