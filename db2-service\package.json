{"name": "querylab-db2-service", "version": "1.0.0", "description": "Serviço dedicado para consultas DB2", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "railway:build": "npm install && npm run build", "railway:start": "npm start", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["db2", "microservice", "sql"], "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/node": "^24.0.14", "axios": "^1.11.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "devDependencies": {"@types/connect": "^3.4.38", "@types/qs": "^6.14.0", "@types/range-parser": "^1.2.7", "@types/send": "^0.17.5", "@types/serve-static": "^1.15.8"}}