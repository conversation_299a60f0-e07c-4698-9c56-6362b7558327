# Configurações para Railway - DB2 Service

# Configuração do serviço
NODE_ENV=production
DB2_SERVICE_PORT=5001

# Configuração do Proxy (RECOMENDADO para DB2 da universidade)
USE_DB2_PROXY=true
DB2_PROXY_URL=https://sua-url-ngrok.ngrok.io
DB2_PROXY_SECRET=sua_chave_secreta_super_forte_123456
DB2_PROXY_TIMEOUT=30000

# Configuração Direta DB2 (apenas se não usar proxy)
# USE_DB2_PROXY=false
# DB2_HOST=ip_do_servidor_db2
# DB2_PORT=50000
# DB2_DATABASE=UNIVDB
# DB2_USERNAME=seu_usuario
# DB2_PASSWORD=sua_senha
# DB2_SSL_ENABLED=false
# DB2_CONNECTION_TIMEOUT=30000
# DB2_QUERY_TIMEOUT=60000

# CORS
CORS_ORIGIN=https://seu-frontend.railway.app
