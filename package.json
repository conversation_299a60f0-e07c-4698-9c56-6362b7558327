{"name": "querylab", "version": "1.0.0", "scripts": {"install:all": "cd frontend && npm install && cd ../backend && npm install", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "build": "npm run build:backend && npm run build:frontend", "start:backend": "cd backend && npm start", "start:frontend": "cd frontend && npm start", "dev": "concurrently \"cd backend && npm run dev\" \"cd frontend && npm run dev\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev"}, "devDependencies": {"concurrently": "^8.2.2"}}