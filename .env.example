# Backend Environment Variables
PORT=5000
NODE_ENV=production
FRONTEND_URL=https://seu-frontend.railway.app

# Database Configuration
DB_TYPE=postgresql
DATABASE_URL=postgresql://username:password@host:port/database

# Para desenvolvimento local (JSON Server)
# DB_TYPE=json-server
# JSON_SERVER_URL=http://localhost:3001

# LLM APIs (Opcional)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
GROQ_API_KEY=your_groq_key_here

# JWT Secret
JWT_SECRET=your_super_secret_jwt_key_here

# CORS
CORS_ORIGIN=https://seu-frontend.railway.app
