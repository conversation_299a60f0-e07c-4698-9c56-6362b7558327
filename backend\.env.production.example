# Configurações de Produção - QueryLab Backend

# Servidor
PORT=5000
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.com

# Banco de Dados da Aplicação
DB_TYPE=json-server
JSON_SERVER_URL=https://your-json-server-url.com

# Banco de Dados para Consultas SQL
QUERY_DB_TYPE=db2-http

# DB2 Service (Microserviço)
# URL do seu DB2 Service deployado
DB2_SERVICE_URL=https://your-db2-service-url.com

# Configurações JWT
JWT_SECRET=your-super-secure-jwt-secret-for-production
JWT_EXPIRES_IN=7d

# Configurações LLM
GROQ_API_KEY=your_production_groq_api_key
OPENAI_API_KEY=your_production_openai_api_key
ANTHROPIC_API_KEY=your_production_anthropic_api_key

# Configurações de CORS (opcional)
CORS_ORIGIN=https://your-frontend-domain.com

# Configurações de Rate Limiting (opcional)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
