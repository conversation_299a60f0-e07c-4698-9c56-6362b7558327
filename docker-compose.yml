version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: querylab
      POSTGRES_USER: querylab
      POSTGRES_PASSWORD: querylab123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U querylab"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Serviço dedicado para DB2
  db2-service:
    build:
      context: ./db2-service
    ports:
      - "5001:5001"
    environment:
      - NODE_ENV=production
      - DB2_HOST=${DB2_HOST}
      - DB2_PORT=${DB2_PORT}
      - DB2_DATABASE=${DB2_DATABASE}
      - DB2_USERNAME=${DB2_USERNAME}
      - DB2_PASSWORD=${DB2_PASSWORD}
      - DB2_SSL_ENABLED=${DB2_SSL_ENABLED}
    restart: unless-stopped

  # Backend API
  backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DB_TYPE=postgresql
      - QUERY_DB_TYPE=db2-http
      - DB2_SERVICE_URL=http://db2-service:5001
      - DATABASE_URL=***********************************************/querylab
      - FRONTEND_URL=http://localhost:3000
      - CORS_ORIGIN=http://localhost:3000
      - JWT_SECRET=your_super_secret_jwt_key_here
    depends_on:
      postgres:
        condition: service_healthy
      db2-service:
        condition: service_started
    volumes:
      - ./backend/.env:/app/.env:ro
    restart: unless-stopped

  # Frontend Next.js
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:5000/api
      - NEXT_PUBLIC_WEBSOCKET_URL=http://localhost:5000
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
